<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Heavenly Experience - สัมผัสประสบการณ์สวรรค์')</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <style>
        :root {
            --heaven-blue: #5c9eff;
            --heaven-light: #b3e5fc;
            --heaven-white: #ffffff;
            --heaven-cloud: #f0f8ff;
            --heaven-glow: #e3f2fd;
            --heaven-dark: #1976d2;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Sarabun', 'Nunito', sans-serif;
            background: linear-gradient(135deg, #87ceeb 0%, #e0f6ff 50%, #ffffff 100%);
            background-attachment: fixed;
            color: var(--heaven-dark);
            line-height: 1.6;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Heaven Background Effects */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 30%, rgba(255,255,255,0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(176,224,230,0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 70%, rgba(255,255,255,0.4) 0%, transparent 30%);
            pointer-events: none;
            z-index: -2;
            animation: heavenGlow 8s ease-in-out infinite alternate;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.8), transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(176,224,230,0.6), transparent),
                radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.9), transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(176,224,230,0.4), transparent);
            background-repeat: repeat;
            background-size: 150px 100px;
            pointer-events: none;
            z-index: -1;
            animation: sparkle 6s linear infinite;
        }

        @keyframes heavenGlow {
            0% { opacity: 0.7; transform: scale(1); }
            100% { opacity: 1; transform: scale(1.02); }
        }

        @keyframes sparkle {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }
        
        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(15px);
            box-shadow: 0 4px 30px rgba(92, 158, 255, 0.2);
            border-bottom: 1px solid rgba(176, 224, 230, 0.3);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--heaven-dark) !important;
            text-shadow: 0 2px 4px rgba(92, 158, 255, 0.3);
        }

        .navbar-nav .nav-link {
            color: var(--heaven-dark) !important;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s ease;
            position: relative;
        }

        .navbar-nav .nav-link:hover {
            color: var(--heaven-blue) !important;
            transform: translateY(-2px);
            text-shadow: 0 2px 8px rgba(92, 158, 255, 0.4);
        }

        .navbar-nav .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--heaven-blue), var(--heaven-light));
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .navbar-nav .nav-link:hover::after {
            width: 80%;
        }
        
        /* Main Content */
        .main-content {
            margin-top: 80px;
            min-height: calc(100vh - 80px);
        }
        
        /* Hero Section */
        .hero-section {
            background:
                linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(176, 224, 230, 0.6) 50%, rgba(255, 255, 255, 0.9) 100%),
                radial-gradient(circle at 30% 20%, rgba(92, 158, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(176, 224, 230, 0.2) 0%, transparent 50%);
            padding: 120px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.6)"/><circle cx="80" cy="40" r="1" fill="rgba(176,224,230,0.8)"/><circle cx="60" cy="80" r="1.5" fill="rgba(255,255,255,0.7)"/></svg>');
            animation: float 20s linear infinite;
            pointer-events: none;
        }

        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            color: var(--heaven-dark);
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(92, 158, 255, 0.3);
            position: relative;
            z-index: 2;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            color: var(--heaven-blue);
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }
        
        /* Card Styles */
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(92, 158, 255, 0.15);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(176, 224, 230, 0.3);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s;
        }

        .card:hover::before {
            left: 100%;
        }

        .card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(92, 158, 255, 0.25);
            border-color: rgba(92, 158, 255, 0.5);
        }

        .card-header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(176, 224, 230, 0.3));
            border-bottom: 1px solid rgba(176, 224, 230, 0.3);
            border-radius: 20px 20px 0 0 !important;
        }
        
        /* Button Styles */
        .btn-primary {
            background: linear-gradient(135deg, var(--heaven-blue), var(--heaven-light));
            border: none;
            border-radius: 30px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(92, 158, 255, 0.3);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(92, 158, 255, 0.4);
            background: linear-gradient(135deg, var(--heaven-dark), var(--heaven-blue));
        }

        .btn-outline-primary {
            border: 2px solid var(--heaven-blue);
            color: var(--heaven-blue);
            border-radius: 30px;
            padding: 10px 25px;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }

        .btn-outline-primary:hover {
            background: var(--heaven-blue);
            color: white;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 25px rgba(92, 158, 255, 0.3);
        }
        
        /* Footer */
        .footer {
            background: linear-gradient(135deg, var(--heaven-dark) 0%, var(--heaven-blue) 50%, var(--heaven-dark) 100%);
            color: white;
            padding: 50px 0 20px;
            margin-top: 50px;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(176,224,230,0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .footer h5 {
            color: var(--heaven-light);
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .footer a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer a:hover {
            color: var(--heaven-light);
            text-shadow: 0 2px 8px rgba(176, 224, 230, 0.5);
            transform: translateX(5px);
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <!-- Header -->
    <nav class="navbar navbar-expand-lg header">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">
                <i class="fas fa-cloud me-2" style="color: var(--heaven-blue);"></i>
                Heavenly Experience
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('home') }}">
                            <i class="fas fa-home me-1"></i>สวรรค์หลัก
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('news.index') }}">
                            <i class="fas fa-feather-alt me-1"></i>ข่าวสารสวรรค์
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">
                            <i class="fas fa-angel me-1"></i>บริการสวรรค์
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">
                            <i class="fas fa-pray me-1"></i>ติดต่อเรา
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/news">
                            <i class="fas fa-crown me-1"></i>จัดการ
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5><i class="fas fa-cloud me-2" style="color: var(--heaven-light);"></i>Heavenly Experience</h5>
                    <p>ประสบการณ์แห่งสวรรค์ที่นำพาคุณสู่ความสุขนิรันดร์ ด้วยความรักและเมตตาอันไร้ขีดจำกัด</p>
                </div>
                <div class="col-md-4 mb-4">
                    <h5><i class="fas fa-feather-alt me-2" style="color: var(--heaven-light);"></i>บริการสวรรค์</h5>
                    <ul class="list-unstyled">
                        <li><a href="#"><i class="fas fa-star me-2"></i>สันติสุขนิรันดร์</a></li>
                        <li><a href="#"><i class="fas fa-star me-2"></i>เสรีภาพแท้จริง</a></li>
                        <li><a href="#"><i class="fas fa-star me-2"></i>แสงแห่งปัญญา</a></li>
                        <li><a href="#"><i class="fas fa-star me-2"></i>ความรักไร้เงื่อนไข</a></li>
                    </ul>
                </div>
                <div class="col-md-4 mb-4">
                    <h5><i class="fas fa-pray me-2" style="color: var(--heaven-light);"></i>ติดต่อเทวดา</h5>
                    <p><i class="fas fa-cloud me-2"></i>เหนือเมฆชั้น 9, สวรรค์ชั้นสูงสุด</p>
                    <p><i class="fas fa-pray me-2"></i>02-HEAVEN-7</p>
                    <p><i class="fas fa-feather-alt me-2"></i>08-ANGEL-99</p>
                    <p><i class="fas fa-dove me-2"></i><EMAIL></p>
                </div>
            </div>
            <hr class="my-4" style="border-color: rgba(176, 224, 230, 0.3);">
            <div class="text-center">
                <p>&copy; {{ date('Y') }} Heavenly Experience. ความสุขนิรันดร์สำหรับทุกวิญญาณ.</p>
                <div class="mt-2">
                    <i class="fas fa-star" style="color: var(--heaven-light); animation: twinkle 2s infinite;"></i>
                    <i class="fas fa-heart mx-2" style="color: var(--heaven-light); animation: twinkle 2s infinite 0.5s;"></i>
                    <i class="fas fa-star" style="color: var(--heaven-light); animation: twinkle 2s infinite 1s;"></i>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    @stack('scripts')
</body>
</html>
