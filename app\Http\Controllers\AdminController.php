<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\News;

class AdminController extends Controller
{
    public function newsIndex()
    {
        $news = News::orderBy('created_at', 'desc')->paginate(15);
        return view('admin.news.index', compact('news'));
    }

    public function newsCreate()
    {
        return view('admin.news.create');
    }

    public function newsStore(Request $request)
    {
        $request->validate([
            'title' => 'required|max:255',
            'content' => 'required',
            'category' => 'required',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            $imageName = time().'.'.$request->image->extension();
            $request->image->move(public_path('images/news'), $imageName);
            $data['image'] = 'images/news/'.$imageName;
        }

        if (!$data['published_at']) {
            $data['published_at'] = now();
        }

        News::create($data);

        return redirect()->route('admin.news.index')->with('success', 'เพิ่มข่าวสารเรียบร้อยแล้ว');
    }

    public function newsEdit($id)
    {
        $news = News::findOrFail($id);
        return view('admin.news.edit', compact('news'));
    }

    public function newsUpdate(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|max:255',
            'content' => 'required',
            'category' => 'required',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $news = News::findOrFail($id);
        $data = $request->all();

        if ($request->hasFile('image')) {
            // ลบรูปเก่า
            if ($news->image && file_exists(public_path($news->image))) {
                unlink(public_path($news->image));
            }

            $imageName = time().'.'.$request->image->extension();
            $request->image->move(public_path('images/news'), $imageName);
            $data['image'] = 'images/news/'.$imageName;
        }

        $news->update($data);

        return redirect()->route('admin.news.index')->with('success', 'แก้ไขข่าวสารเรียบร้อยแล้ว');
    }

    public function newsDestroy($id)
    {
        $news = News::findOrFail($id);

        // ลบรูป
        if ($news->image && file_exists(public_path($news->image))) {
            unlink(public_path($news->image));
        }

        $news->delete();

        return redirect()->route('admin.news.index')->with('success', 'ลบข่าวสารเรียบร้อยแล้ว');
    }
}
