@extends('layouts.app')

@section('title', 'สวรรค์หลัก - Heavenly Experience')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center fade-in-up">
                <h1 class="hero-title">
                    <i class="fas fa-cloud mb-3 d-block" style="font-size: 4rem; color: var(--heaven-blue); animation: float 3s ease-in-out infinite alternate;"></i>
                    ยินดีต้อนรับสู่ประสบการณ์สวรรค์
                </h1>
                <p class="hero-subtitle">
                    สัมผัสความสุข ความสงบ และความงดงามที่เหนือจินตนาการ<br>
                    ที่นี่คือจุดหมายปลายทางแห่งความสุขนิรันดร์
                </p>
                <div class="mt-4">
                    <a href="#services" class="btn btn-primary me-3">
                        <i class="fas fa-feather-alt me-2"></i>สำรวจสวรรค์
                    </a>
                    <a href="#contact" class="btn btn-outline-primary">
                        <i class="fas fa-pray me-2"></i>ติดต่อเทวดา
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section id="services" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold mb-3" style="color: var(--heaven-dark);">บริการสวรรค์ของเรา</h2>
                <p class="lead" style="color: var(--heaven-blue);">ประสบการณ์แห่งความสุขและความสงบที่เหนือจินตนาการ</p>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-dove fa-3x mb-3" style="color: var(--heaven-blue);"></i>
                        <h5 class="card-title" style="color: var(--heaven-dark);">สันติสุขนิรันดร์</h5>
                        <p class="card-text">สัมผัสความสงบและความสุขที่ไม่มีวันสิ้นสุด ในโลกแห่งความสมบูรณ์</p>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-feather-alt fa-3x mb-3" style="color: var(--heaven-blue);"></i>
                        <h5 class="card-title" style="color: var(--heaven-dark);">เสรีภาพแท้จริง</h5>
                        <p class="card-text">ปลดปล่อยจากทุกข์โศก บินสู่อิสรภาพที่ไร้ขีดจำกัด</p>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-star fa-3x mb-3" style="color: var(--heaven-blue);"></i>
                        <h5 class="card-title" style="color: var(--heaven-dark);">แสงแห่งปัญญา</h5>
                        <p class="card-text">ค้นพบความจริงแท้ และความรู้อันไร้ขีดจำกัดในแสงแห่งสวรรค์</p>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-heart fa-3x mb-3" style="color: var(--heaven-blue);"></i>
                        <h5 class="card-title" style="color: var(--heaven-dark);">ความรักไร้เงื่อนไข</h5>
                        <p class="card-text">สัมผัสความรักที่บริสุทธิ์และไร้เงื่อนไข จากหัวใจแห่งสวรรค์</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- News Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold mb-3" style="color: var(--heaven-dark);">ข่าวสารจากสวรรค์</h2>
                <p class="lead" style="color: var(--heaven-blue);">ข้อความแห่งความหวังและแรงบันดาลใจจากเหนือเมฆ</p>
            </div>
        </div>
        
        <div class="row g-4">
            @php
                $sampleNews = [
                    [
                        'title' => 'การเดินทางสู่สวรรค์ในยุคใหม่',
                        'excerpt' => 'ค้นพบเส้นทางใหม่สู่ความสุขนิรันดร์ด้วยเทคโนโลยีแห่งสวรรค์',
                        'date' => '15 มิ.ย. 2567',
                        'image' => 'https://via.placeholder.com/400x250/87ceeb/ffffff?text=Heaven+News+1'
                    ],
                    [
                        'title' => 'ปรัชญาแห่งความสงบ',
                        'excerpt' => 'เรียนรู้หลักธรรมแห่งสันติสุขและการปลดปล่อยจากทุกข์โศก',
                        'date' => '10 มิ.ย. 2567',
                        'image' => 'https://via.placeholder.com/400x250/b3e5fc/ffffff?text=Heaven+News+2'
                    ],
                    [
                        'title' => 'บริการใหม่: การเชื่อมต่อกับเทวดา',
                        'excerpt' => 'เปิดช่องทางการสื่อสารกับผู้ปกครองแห่งสวรรค์ตลอด 24 ชั่วโมง',
                        'date' => '5 มิ.ย. 2567',
                        'image' => 'https://via.placeholder.com/400x250/e0f6ff/5c9eff?text=Heaven+News+3'
                    ]
                ];
            @endphp
            
            @foreach($sampleNews as $news)
            <div class="col-md-6 col-lg-4">
                <div class="card h-100">
                    <img src="{{ $news['image'] }}" class="card-img-top" alt="{{ $news['title'] }}" style="height: 200px; object-fit: cover;">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ $news['title'] }}</h5>
                        <p class="card-text flex-grow-1">{{ $news['excerpt'] }}</p>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>{{ $news['date'] }}
                            </small>
                            <a href="#" class="btn btn-outline-primary btn-sm">อ่านต่อ</a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-5">
            <a href="{{ route('news.index') }}" class="btn btn-primary">
                <i class="fas fa-feather-alt me-2"></i>ดูข่าวสารสวรรค์ทั้งหมด
            </a>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section id="contact" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold mb-3" style="color: var(--heaven-dark);">ติดต่อเทวดาผู้ดูแล</h2>
                <p class="lead" style="color: var(--heaven-blue);">พร้อมให้การดูแลและคำแนะนำตลอดเวลา</p>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-6 col-lg-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-pray fa-3x mb-3" style="color: var(--heaven-blue);"></i>
                        <h5 class="card-title" style="color: var(--heaven-dark);">สายด่วนสวรรค์</h5>
                        <p class="card-text">
                            <strong style="color: var(--heaven-blue);">02-HEAVEN-7</strong><br>
                            <small style="color: var(--heaven-blue);">เชื่อมต่อตลอด 24 ชม.</small>
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-feather-alt fa-3x mb-3" style="color: var(--heaven-blue);"></i>
                        <h5 class="card-title" style="color: var(--heaven-dark);">เทวดาเคลื่อนที่</h5>
                        <p class="card-text">
                            <strong style="color: var(--heaven-blue);">08-ANGEL-99</strong><br>
                            <small style="color: var(--heaven-blue);">พร้อมให้คำปรึกษา</small>
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-dove fa-3x mb-3" style="color: var(--heaven-blue);"></i>
                        <h5 class="card-title" style="color: var(--heaven-dark);">จดหมายสวรรค์</h5>
                        <p class="card-text">
                            <strong style="color: var(--heaven-blue);"><EMAIL></strong><br>
                            <small style="color: var(--heaven-blue);">ตอบกลับทันที</small>
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-cloud fa-3x mb-3" style="color: var(--heaven-blue);"></i>
                        <h5 class="card-title" style="color: var(--heaven-dark);">ที่อยู่สวรรค์</h5>
                        <p class="card-text">
                            <strong style="color: var(--heaven-blue);">เหนือเมฆชั้น 9</strong><br>
                            <small style="color: var(--heaven-blue);">สวรรค์ชั้นสูงสุด</small>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold mb-3" style="color: var(--heaven-dark);">ทำไมต้องเลือกสวรรค์ของเรา</h2>
                <p class="lead" style="color: var(--heaven-blue);">ความไว้วางใจจากวิญญาณกว่า 1,000,000 ดวง</p>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-6 col-lg-4">
                <div class="text-center">
                    <div class="rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                         style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--heaven-blue), var(--heaven-light)); box-shadow: 0 10px 25px rgba(92, 158, 255, 0.3);">
                        <i class="fas fa-infinity fa-2x text-white"></i>
                    </div>
                    <h5 style="color: var(--heaven-dark);">ประสบการณ์นิรันดร์</h5>
                    <p style="color: var(--heaven-blue);">ประสบการณ์ไร้ขีดจำกัดในการดูแลวิญญาณ</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-4">
                <div class="text-center">
                    <div class="rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                         style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--heaven-blue), var(--heaven-light)); box-shadow: 0 10px 25px rgba(92, 158, 255, 0.3);">
                        <i class="fas fa-sun fa-2x text-white"></i>
                    </div>
                    <h5 style="color: var(--heaven-dark);">แสงสว่างตลอดกาล</h5>
                    <p style="color: var(--heaven-blue);">ไม่มีความมืด ไม่มีความเศร้า มีแต่แสงแห่งความสุข</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-4">
                <div class="text-center">
                    <div class="rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                         style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--heaven-blue), var(--heaven-light)); box-shadow: 0 10px 25px rgba(92, 158, 255, 0.3);">
                        <i class="fas fa-hands-praying fa-2x text-white"></i>
                    </div>
                    <h5 style="color: var(--heaven-dark);">ดูแลด้วยเมตตา</h5>
                    <p style="color: var(--heaven-blue);">ให้บริการด้วยความรักและเมตตาอันไร้ขีดจำกัด</p>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
    /* Additional Heaven Effects for Home Page */
    .fade-in-up {
        animation: fadeInUp 1s ease-out, float 6s ease-in-out infinite 2s;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    /* Glowing text effect */
    .hero-title {
        animation: titleGlow 4s ease-in-out infinite alternate;
    }

    @keyframes titleGlow {
        0% { text-shadow: 0 4px 8px rgba(92, 158, 255, 0.3); }
        100% { text-shadow: 0 4px 20px rgba(92, 158, 255, 0.6), 0 0 30px rgba(176, 224, 230, 0.4); }
    }

    /* Card hover effects */
    .card {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .card:hover {
        animation: cardFloat 2s ease-in-out infinite;
    }

    @keyframes cardFloat {
        0%, 100% { transform: translateY(-15px) scale(1.02); }
        50% { transform: translateY(-20px) scale(1.02); }
    }

    /* Icon animations */
    .card i {
        transition: all 0.3s ease;
    }

    .card:hover i {
        animation: iconSpin 2s linear infinite;
        filter: drop-shadow(0 0 10px rgba(92, 158, 255, 0.5));
    }

    @keyframes iconSpin {
        0% { transform: rotate(0deg) scale(1); }
        50% { transform: rotate(180deg) scale(1.1); }
        100% { transform: rotate(360deg) scale(1); }
    }

    /* Background enhancement */
    .bg-light {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(176, 224, 230, 0.3) 50%, rgba(255, 255, 255, 0.9) 100%) !important;
    }
</style>
@endpush

@push('scripts')
<script>
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add animation on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.card, .hero-section').forEach(el => {
        observer.observe(el);
    });
</script>
@endpush
