@extends('layouts.app')

@section('title', 'แก้ไขข่าว: ' . $news->title . ' - Admin')

@section('content')
<!-- Page Header -->
<section class="py-4 bg-warning text-dark">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-edit me-2"></i>แก้ไขข่าว
                </h1>
                <p class="mb-0 opacity-75">แก้ไขข้อมูลข่าวสาร: {{ $news->title }}</p>
            </div>
            <div class="col-auto">
                <div class="btn-group">
                    <a href="{{ route('news.show', $news->id) }}" class="btn btn-outline-dark" target="_blank">
                        <i class="fas fa-eye me-2"></i>ดูข่าว
                    </a>
                    <a href="{{ route('admin.news.index') }}" class="btn btn-dark">
                        <i class="fas fa-arrow-left me-2"></i>กลับไปจัดการข่าว
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Form Section -->
<section class="py-4">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i>แก้ไขข้อมูลข่าวสาร
                        </h5>
                    </div>
                    
                    <div class="card-body">
                        @if($errors->any())
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>กรุณาแก้ไขข้อผิดพลาดต่อไปนี้:</h6>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                        @endif
                        
                        <form action="{{ route('admin.news.update', $news->id) }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')
                            
                            <!-- Title -->
                            <div class="mb-3">
                                <label for="title" class="form-label">
                                    หัวข้อข่าว <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                       id="title" name="title" value="{{ old('title', $news->title) }}" 
                                       placeholder="กรอกหัวข้อข่าวสาร" required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Category -->
                            <div class="mb-3">
                                <label for="category" class="form-label">
                                    หมวดหมู่ <span class="text-danger">*</span>
                                </label>
                                <select class="form-select @error('category') is-invalid @enderror" 
                                        id="category" name="category" required>
                                    <option value="">เลือกหมวดหมู่</option>
                                    <option value="ข่าวทั่วไป" {{ old('category', $news->category) == 'ข่าวทั่วไป' ? 'selected' : '' }}>ข่าวทั่วไป</option>
                                    <option value="บริการใหม่" {{ old('category', $news->category) == 'บริการใหม่' ? 'selected' : '' }}>บริการใหม่</option>
                                    <option value="แนวทางปฏิบัติ" {{ old('category', $news->category) == 'แนวทางปฏิบัติ' ? 'selected' : '' }}>แนวทางปฏิบัติ</option>
                                    <option value="ประเพณีไทย" {{ old('category', $news->category) == 'ประเพณีไทย' ? 'selected' : '' }}>ประเพณีไทย</option>
                                    <option value="พิธีกรรม" {{ old('category', $news->category) == 'พิธีกรรม' ? 'selected' : '' }}>พิธีกรรม</option>
                                    <option value="คำแนะนำ" {{ old('category', $news->category) == 'คำแนะนำ' ? 'selected' : '' }}>คำแนะนำ</option>
                                    <option value="สุขภาพจิต" {{ old('category', $news->category) == 'สุขภาพจิต' ? 'selected' : '' }}>สุขภาพจิต</option>
                                </select>
                                @error('category')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Excerpt -->
                            <div class="mb-3">
                                <label for="excerpt" class="form-label">สรุปข่าว</label>
                                <textarea class="form-control @error('excerpt') is-invalid @enderror" 
                                          id="excerpt" name="excerpt" rows="3" 
                                          placeholder="สรุปสั้นๆ ของข่าวสาร (ถ้าไม่กรอกจะใช้ข้อความต้นของเนื้อหา)">{{ old('excerpt', $news->excerpt) }}</textarea>
                                @error('excerpt')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Content -->
                            <div class="mb-3">
                                <label for="content" class="form-label">
                                    เนื้อหาข่าว <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control @error('content') is-invalid @enderror" 
                                          id="content" name="content" rows="10" 
                                          placeholder="เขียนเนื้อหาข่าวสารที่ต้องการแจ้งให้ทราบ" required>{{ old('content', $news->content) }}</textarea>
                                @error('content')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Current Image -->
                            @if($news->image)
                            <div class="mb-3">
                                <label class="form-label">รูปภาพปัจจุบัน</label>
                                <div class="d-flex align-items-center gap-3">
                                    <img src="{{ asset($news->image) }}" class="img-thumbnail" style="max-width: 200px;">
                                    <div>
                                        <p class="mb-1"><strong>{{ basename($news->image) }}</strong></p>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image" value="1">
                                            <label class="form-check-label text-danger" for="remove_image">
                                                <i class="fas fa-trash me-1"></i>ลบรูปภาพนี้
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif
                            
                            <!-- New Image -->
                            <div class="mb-3">
                                <label for="image" class="form-label">
                                    {{ $news->image ? 'เปลี่ยนรูปภาพใหม่' : 'รูปภาพประกอบ' }}
                                </label>
                                <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                       id="image" name="image" accept="image/*">
                                <div class="form-text">
                                    รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB
                                </div>
                                @error('image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Image Preview -->
                            <div class="mb-3" id="imagePreview" style="display: none;">
                                <label class="form-label">ตัวอย่างรูปภาพใหม่</label>
                                <div>
                                    <img id="previewImg" src="" class="img-thumbnail" style="max-width: 300px;">
                                </div>
                            </div>
                            
                            <!-- Options -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="is_featured" name="is_featured" value="1"
                                               {{ old('is_featured', $news->is_featured) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_featured">
                                            <i class="fas fa-star text-warning me-1"></i>
                                            ข่าวเด่น (แสดงในหน้าแรก)
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="is_published" name="is_published" value="1"
                                               {{ old('is_published', $news->is_published) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_published">
                                            <i class="fas fa-eye text-success me-1"></i>
                                            เผยแพร่
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Published Date -->
                            <div class="mb-4">
                                <label for="published_at" class="form-label">วันที่เผยแพร่</label>
                                <input type="datetime-local" class="form-control @error('published_at') is-invalid @enderror" 
                                       id="published_at" name="published_at" 
                                       value="{{ old('published_at', $news->published_at ? $news->published_at->format('Y-m-d\TH:i') : now()->format('Y-m-d\TH:i')) }}">
                                @error('published_at')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Submit Buttons -->
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                                </button>
                                <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>ยกเลิก
                                </a>
                                <a href="{{ route('news.show', $news->id) }}" class="btn btn-outline-info" target="_blank">
                                    <i class="fas fa-eye me-2"></i>ดูข่าว
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Info Card -->
                <div class="card mt-4">
                    <div class="card-header bg-light">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>ข้อมูลข่าว
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>สร้างเมื่อ:</strong> {{ $news->created_at->format('d/m/Y H:i น.') }}</p>
                                <p><strong>แก้ไขล่าสุด:</strong> {{ $news->updated_at->format('d/m/Y H:i น.') }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>สถานะ:</strong> 
                                    @if($news->is_published)
                                        <span class="badge bg-success">เผยแพร่แล้ว</span>
                                    @else
                                        <span class="badge bg-secondary">ร่าง</span>
                                    @endif
                                </p>
                                <p><strong>ข่าวเด่น:</strong> 
                                    @if($news->is_featured)
                                        <span class="badge bg-warning text-dark">ใช่</span>
                                    @else
                                        <span class="badge bg-light text-dark">ไม่ใช่</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    // Image preview
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
        }
    });
    
    // Auto-resize textarea
    document.getElementById('content').addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Character counter for title
    const titleInput = document.getElementById('title');
    const titleLabel = titleInput.previousElementSibling;
    
    titleInput.addEventListener('input', function() {
        const length = this.value.length;
        const maxLength = 255;
        const remaining = maxLength - length;
        
        if (remaining < 50) {
            titleLabel.innerHTML = `หัวข้อข่าว <span class="text-danger">*</span> <small class="text-muted">(เหลือ ${remaining} ตัวอักษร)</small>`;
        } else {
            titleLabel.innerHTML = `หัวข้อข่าว <span class="text-danger">*</span>`;
        }
    });
    
    // Remove image checkbox
    document.getElementById('remove_image')?.addEventListener('change', function() {
        const imageInput = document.getElementById('image');
        if (this.checked) {
            imageInput.required = false;
            imageInput.parentElement.querySelector('label').textContent = 'รูปภาพใหม่ (จำเป็น เนื่องจากจะลบรูปเก่า)';
        } else {
            imageInput.required = false;
            imageInput.parentElement.querySelector('label').textContent = 'เปลี่ยนรูปภาพใหม่';
        }
    });
</script>
@endpush
