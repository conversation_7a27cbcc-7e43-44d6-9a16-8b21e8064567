@extends('layouts.app')

@section('title', 'ข่าวสาร - บริการรับจัดงานศพแบบครบวงจร')

@section('content')
<!-- Page Header -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="fas fa-newspaper me-3"></i>ข่าวสาร
                </h1>
                <p class="lead text-muted">ติดตามข่าวสารและข้อมูลที่เป็นประโยชน์</p>
            </div>
        </div>
    </div>
</section>

<!-- Search and Filter Section -->
<section class="py-4 bg-white border-bottom">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <form method="GET" action="{{ route('news.index') }}" class="row g-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" name="search" 
                                   placeholder="ค้นหาข่าวสาร..." 
                                   value="{{ request('search') }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select name="category" class="form-select">
                            <option value="">ทุกหมวดหมู่</option>
                            @if(isset($categories))
                                @foreach($categories as $category)
                                    <option value="{{ $category }}" 
                                            {{ request('category') == $category ? 'selected' : '' }}>
                                        {{ $category }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i>ค้นหา
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- News Content -->
<section class="py-5">
    <div class="container">
        @if(isset($news) && $news->count() > 0)
            <div class="row g-4">
                @foreach($news as $item)
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm">
                        @if($item->image)
                            <img src="{{ asset($item->image) }}" class="card-img-top" 
                                 alt="{{ $item->title }}" style="height: 200px; object-fit: cover;">
                        @else
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                 style="height: 200px;">
                                <i class="fas fa-newspaper fa-3x text-muted"></i>
                            </div>
                        @endif
                        
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <span class="badge bg-primary">{{ $item->category }}</span>
                                @if($item->is_featured)
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-star me-1"></i>เด่น
                                    </span>
                                @endif
                            </div>
                            
                            <h5 class="card-title">{{ $item->title }}</h5>
                            <p class="card-text flex-grow-1">{{ $item->excerpt }}</p>
                            
                            <div class="d-flex justify-content-between align-items-center mt-auto">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>{{ $item->formatted_date }}
                                </small>
                                <a href="{{ route('news.show', $item->id) }}" class="btn btn-outline-primary btn-sm">
                                    อ่านต่อ <i class="fas fa-arrow-right ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-5">
                {{ $news->appends(request()->query())->links() }}
            </div>
        @else
            <!-- No News Found -->
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-newspaper fa-5x text-muted mb-4"></i>
                        <h3 class="text-muted">ไม่พบข่าวสาร</h3>
                        <p class="text-muted">
                            @if(request('search') || request('category'))
                                ไม่พบข่าวสารที่ตรงกับเงื่อนไขการค้นหา
                            @else
                                ยังไม่มีข่าวสารในขณะนี้
                            @endif
                        </p>
                        @if(request('search') || request('category'))
                            <a href="{{ route('news.index') }}" class="btn btn-primary">
                                <i class="fas fa-refresh me-2"></i>ดูข่าวสารทั้งหมด
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Sample News for Demo -->
@if(!isset($news) || $news->count() == 0)
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-6 fw-bold mb-3">ข่าวสารตัวอย่าง</h2>
                <p class="lead text-muted">ข่าวสารและข้อมูลที่เป็นประโยชน์</p>
            </div>
        </div>
        
        <div class="row g-4">
            @php
                $sampleNews = [
                    [
                        'title' => 'แนวทางการจัดงานศพในยุค New Normal',
                        'excerpt' => 'ข้อแนะนำและแนวปฏิบัติในการจัดงานศพให้ปลอดภัยจากโควิด-19 พร้อมมาตรการป้องกันที่เหมาะสม',
                        'date' => '15 มิ.ย. 2567',
                        'category' => 'แนวทางปฏิบัติ',
                        'image' => 'https://via.placeholder.com/400x250/f8f9fa/6c757d?text=COVID+Safety'
                    ],
                    [
                        'title' => 'ประเพณีการจัดงานศพแบบไทย',
                        'excerpt' => 'ความหมายและขั้นตอนของการจัดงานศพตามประเพณีไทยโบราณ รวมถึงพิธีกรรมที่สำคัญ',
                        'date' => '10 มิ.ย. 2567',
                        'category' => 'ประเพณีไทย',
                        'image' => 'https://via.placeholder.com/400x250/f8f9fa/6c757d?text=Thai+Tradition'
                    ],
                    [
                        'title' => 'บริการใหม่: ถ่ายทอดสดพิธีศพ',
                        'excerpt' => 'เปิดบริการถ่ายทอดสดพิธีศพสำหรับญาติที่ไม่สามารถเดินทางมาได้ ด้วยเทคโนโลยีที่ทันสมัย',
                        'date' => '5 มิ.ย. 2567',
                        'category' => 'บริการใหม่',
                        'image' => 'https://via.placeholder.com/400x250/f8f9fa/6c757d?text=Live+Streaming'
                    ],
                    [
                        'title' => 'การเลือกโลงศพที่เหมาะสม',
                        'excerpt' => 'คำแนะนำในการเลือกโลงศพให้เหมาะสมกับงบประมาณและความต้องการของครอบครัว',
                        'date' => '1 มิ.ย. 2567',
                        'category' => 'คำแนะนำ',
                        'image' => 'https://via.placeholder.com/400x250/f8f9fa/6c757d?text=Coffin+Guide'
                    ],
                    [
                        'title' => 'พิธีกรรมทางศาสนาในงานศพ',
                        'excerpt' => 'ความสำคัญของพิธีกรรมทางศาสนาในงานศพ และการเตรียมความพร้อมที่เหมาะสม',
                        'date' => '25 พ.ค. 2567',
                        'category' => 'พิธีกรรม',
                        'image' => 'https://via.placeholder.com/400x250/f8f9fa/6c757d?text=Religious+Ceremony'
                    ],
                    [
                        'title' => 'การดูแลจิตใจในช่วงไว้อาลัย',
                        'excerpt' => 'คำแนะนำในการดูแลสุขภาพจิตของตนเองและครอบครัวในช่วงเวลาที่ยากลำบาก',
                        'date' => '20 พ.ค. 2567',
                        'category' => 'สุขภาพจิต',
                        'image' => 'https://via.placeholder.com/400x250/f8f9fa/6c757d?text=Mental+Health'
                    ]
                ];
            @endphp
            
            @foreach($sampleNews as $item)
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 shadow-sm">
                    <img src="{{ $item['image'] }}" class="card-img-top" 
                         alt="{{ $item['title'] }}" style="height: 200px; object-fit: cover;">
                    
                    <div class="card-body d-flex flex-column">
                        <div class="mb-2">
                            <span class="badge bg-primary">{{ $item['category'] }}</span>
                        </div>
                        
                        <h5 class="card-title">{{ $item['title'] }}</h5>
                        <p class="card-text flex-grow-1">{{ $item['excerpt'] }}</p>
                        
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>{{ $item['date'] }}
                            </small>
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                อ่านต่อ <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif
@endsection

@push('styles')
<style>
    .card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    }
    
    .badge {
        font-size: 0.75rem;
    }
    
    .btn-outline-primary:hover {
        transform: translateX(2px);
    }
</style>
@endpush
