body {
  min-height: 100vh;
  background: linear-gradient(135deg, #181c24 0%, #23272f 100%);
  background-attachment: fixed;
  color: #e3f2fd;
  font-family: '<PERSON>bu<PERSON>', '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
  overflow-x: hidden;
}

.heaven-bg {
  position: fixed;
  top: 0; left: 0; width: 100vw; height: 100vh;
  pointer-events: none;
  z-index: 0;
  background: radial-gradient(circle at 60% 20%, rgba(60,80,120,0.3) 0%, rgba(24,28,36,0.7) 60%, transparent 100%),
              linear-gradient(120deg, rgba(30,40,60,0.5) 0%, rgba(35,39,47,0.2) 100%);
  animation: heavenGlow 8s ease-in-out infinite alternate;
}

@keyframes heavenGlow {
  0% { filter: blur(0px) brightness(1); }
  100% { filter: blur(8px) brightness(1.08); }
}

.heaven-glitter {
  position: fixed;
  top: 0; left: 0; width: 100vw; height: 100vh;
  pointer-events: none;
  z-index: 1;
  background-image: url('data:image/svg+xml;utf8,<svg width="100%25" height="100%25" xmlns="http://www.w3.org/2000/svg"><circle cx="10" cy="10" r="1.5" fill="%235c9eff"/><circle cx="80" cy="40" r="1" fill="%23fff"/><circle cx="200" cy="120" r="1.2" fill="%235c9eff"/><circle cx="300" cy="80" r="1.5" fill="%23b3e5fc"/><circle cx="500" cy="200" r="1.2" fill="%23fff"/><circle cx="700" cy="50" r="1.5" fill="%235c9eff"/></svg>');
  opacity: 0.3;
  animation: glitterMove 12s linear infinite alternate;
}

@keyframes glitterMove {
  0% { background-position: 0 0; }
  100% { background-position: 100px 60px; }
}

.heaven-title {
  font-size: 3rem;
  font-weight: 800;
  color: #5c9eff;
  text-shadow: 0 4px 24px #0d1b2a, 0 1px 0 #fff;
  letter-spacing: 2px;
  margin-bottom: 1rem;
  animation: floatTitle 3s ease-in-out infinite alternate;
}

@keyframes floatTitle {
  0% { transform: translateY(0); }
  100% { transform: translateY(-12px); }
}

.heaven-btn {
  background: rgba(30,40,60,0.7);
  border: 2px solid #5c9eff;
  color: #e3f2fd;
  border-radius: 30px;
  padding: 0.75rem 2.5rem;
  font-size: 1.2rem;
  font-weight: 700;
  box-shadow: 0 2px 16px #5c9eff44;
  transition: all 0.3s cubic-bezier(.4,2,.6,1);
  backdrop-filter: blur(4px);
}
.heaven-btn:hover {
  background: #23272f;
  color: #5c9eff;
  box-shadow: 0 4px 32px #5c9eff88;
  transform: translateY(-2px) scale(1.04);
}

.heaven-card {
  background: rgba(30,40,60,0.85);
  border-radius: 24px;
  box-shadow: 0 8px 32px #5c9eff22;
  padding: 2rem 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #23272f;
  transition: box-shadow 0.3s;
  color: #e3f2fd;
}
.heaven-card:hover {
  box-shadow: 0 16px 48px #5c9eff55;
}

::-webkit-scrollbar {
  width: 8px;
  background: #23272f;
}
::-webkit-scrollbar-thumb {
  background: #5c9eff;
  border-radius: 8px;
}
